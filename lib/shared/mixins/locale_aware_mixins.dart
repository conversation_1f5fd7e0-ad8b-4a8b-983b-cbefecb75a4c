import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';

/// Mixin that automatically handles initialization and disposal
/// This version automatically sets up and tears down the locale subscription
mixin LocaleAwareScreenMixin<T extends StatefulWidget> on State<T> {
  StreamSubscription<String>? _localeSubscription;

  @override
  void initState() {
    super.initState();
    _initLocaleAware();
  }

  void _initLocaleAware() {
    _localeSubscription = getIt<UserCubit>().currentLocaleController.stream.listen((String locale) {
      if (mounted) {
        onLocaleChanged(locale);
      }
    });
  }

  /// Called when locale changes
  /// Override this method to handle locale changes in your screen
  void onLocaleChanged(String locale);

  @override
  void dispose() {
    _localeSubscription?.cancel();
    _localeSubscription = null;
    super.dispose();
  }
}

/// Abstract class version for cubits that prefer inheritance over mixin
abstract class LocaleAwareCubit<T extends Object> extends Cubit<T> {
  late StreamSubscription<String> _localeSubscription;

  LocaleAwareCubit(super.initialState) {
    _localeSubscription = getIt<UserCubit>().currentLocaleController.stream.listen((String locale) {
      onLocaleChanged(locale);
    });
  }

  /// Called when locale changes
  void onLocaleChanged(String locale);

  @override
  Future<void> close() {
    _localeSubscription.cancel();
    return super.close();
  }
}
