import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_info_model.freezed.dart';
part 'app_info_model.g.dart';

@freezed
class AppInfoModel with _$AppInfoModel {
  const factory AppInfoModel({
    @Default("") String content,
    @Default(0) int id,
    @Default("") String title,
    @Default("https://media.istockphoto.com/id/905147220/vector/grunge-red-official-round-rubber-seal-stamp-on-white-background.jpg?s=612x612&w=0&k=20&c=9UuLhyqlNyaWTUQrhRZ_sRsSC1xmvLLADsTYlZRjzS4=") String sealImageUrl,
    @Default(0) int type,
  }) = _AppInfoModel;

  factory AppInfoModel.fromJson(Map<String, dynamic> json) => _$AppInfoModelFromJson(json);
}
