// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AppInfoModel _$AppInfoModelFromJson(Map<String, dynamic> json) {
  return _AppInfoModel.fromJson(json);
}

/// @nodoc
mixin _$AppInfoModel {
  String get content => throw _privateConstructorUsedError;
  int get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get sealImageUrl => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;

  /// Serializes this AppInfoModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppInfoModelCopyWith<AppInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppInfoModelCopyWith<$Res> {
  factory $AppInfoModelCopyWith(
          AppInfoModel value, $Res Function(AppInfoModel) then) =
      _$AppInfoModelCopyWithImpl<$Res, AppInfoModel>;
  @useResult
  $Res call(
      {String content, int id, String title, String sealImageUrl, int type});
}

/// @nodoc
class _$AppInfoModelCopyWithImpl<$Res, $Val extends AppInfoModel>
    implements $AppInfoModelCopyWith<$Res> {
  _$AppInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = null,
    Object? id = null,
    Object? title = null,
    Object? sealImageUrl = null,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      sealImageUrl: null == sealImageUrl
          ? _value.sealImageUrl
          : sealImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppInfoModelImplCopyWith<$Res>
    implements $AppInfoModelCopyWith<$Res> {
  factory _$$AppInfoModelImplCopyWith(
          _$AppInfoModelImpl value, $Res Function(_$AppInfoModelImpl) then) =
      __$$AppInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String content, int id, String title, String sealImageUrl, int type});
}

/// @nodoc
class __$$AppInfoModelImplCopyWithImpl<$Res>
    extends _$AppInfoModelCopyWithImpl<$Res, _$AppInfoModelImpl>
    implements _$$AppInfoModelImplCopyWith<$Res> {
  __$$AppInfoModelImplCopyWithImpl(
      _$AppInfoModelImpl _value, $Res Function(_$AppInfoModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = null,
    Object? id = null,
    Object? title = null,
    Object? sealImageUrl = null,
    Object? type = null,
  }) {
    return _then(_$AppInfoModelImpl(
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      sealImageUrl: null == sealImageUrl
          ? _value.sealImageUrl
          : sealImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppInfoModelImpl implements _AppInfoModel {
  const _$AppInfoModelImpl(
      {this.content = "",
      this.id = 0,
      this.title = "",
      this.sealImageUrl = '',
      this.type = 0});

  factory _$AppInfoModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppInfoModelImplFromJson(json);

  @override
  @JsonKey()
  final String content;
  @override
  @JsonKey()
  final int id;
  @override
  @JsonKey()
  final String title;
  @override
  @JsonKey()
  final String sealImageUrl;
  @override
  @JsonKey()
  final int type;

  @override
  String toString() {
    return 'AppInfoModel(content: $content, id: $id, title: $title, sealImageUrl: $sealImageUrl, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppInfoModelImpl &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.sealImageUrl, sealImageUrl) ||
                other.sealImageUrl == sealImageUrl) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, content, id, title, sealImageUrl, type);

  /// Create a copy of AppInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppInfoModelImplCopyWith<_$AppInfoModelImpl> get copyWith =>
      __$$AppInfoModelImplCopyWithImpl<_$AppInfoModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppInfoModelImplToJson(
      this,
    );
  }
}

abstract class _AppInfoModel implements AppInfoModel {
  const factory _AppInfoModel(
      {final String content,
      final int id,
      final String title,
      final String sealImageUrl,
      final int type}) = _$AppInfoModelImpl;

  factory _AppInfoModel.fromJson(Map<String, dynamic> json) =
      _$AppInfoModelImpl.fromJson;

  @override
  String get content;
  @override
  int get id;
  @override
  String get title;
  @override
  String get sealImageUrl;
  @override
  int get type;

  /// Create a copy of AppInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppInfoModelImplCopyWith<_$AppInfoModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
