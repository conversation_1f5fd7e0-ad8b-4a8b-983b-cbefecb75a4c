// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		000DE0162E4C7D450038CA8A /* exportOptions_xyzq.plist in Resources */ = {isa = PBXBuildFile; fileRef = 000DE0152E4C7D450038CA8A /* exportOptions_xyzq.plist */; };
		00228A7A2E38A4CF0036B054 /* exportOptions_dyzb.plist in Resources */ = {isa = PBXBuildFile; fileRef = 00228A792E38A4CF0036B054 /* exportOptions_dyzb.plist */; };
		00499B882DB67F2A0022EDD6 /* exportOptions_gp.plist in Resources */ = {isa = PBXBuildFile; fileRef = 00499B852DB67F2A0022EDD6 /* exportOptions_gp.plist */; };
		00499B892DB67F2A0022EDD6 /* exportOptions_rsyp.plist in Resources */ = {isa = PBXBuildFile; fileRef = 00499B862DB67F2A0022EDD6 /* exportOptions_rsyp.plist */; };
		004A6D982DCB9E57003AB024 /* exportOptions_pre.plist in Resources */ = {isa = PBXBuildFile; fileRef = 004A6D972DCB9E57003AB024 /* exportOptions_pre.plist */; };
		00610DFC2DE445030050C2E4 /* exportOptions_yhxt.plist in Resources */ = {isa = PBXBuildFile; fileRef = 00610DFB2DE445030050C2E4 /* exportOptions_yhxt.plist */; };
		0069894D2E27911800CB0F4A /* exportOptions_tempa.plist in Resources */ = {isa = PBXBuildFile; fileRef = 0069894C2E27911800CB0F4A /* exportOptions_tempa.plist */; };
		006989552E27954000CB0F4A /* exportOptions_bszb.plist in Resources */ = {isa = PBXBuildFile; fileRef = 006989542E27954000CB0F4A /* exportOptions_bszb.plist */; };
		00C66E322E12A8F1007B75D9 /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		0277366F835DAEC26FA6DBF3 /* xyzqRelease.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 3410630F0A72851C1A4DFBD7 /* xyzqRelease.xcconfig */; };
		03733C236B0DFB3884EFBEE2 /* Pods_RunnerTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AEE111FB2B936CCCF33EA119 /* Pods_RunnerTests.framework */; };
		0435542A0B793D3BAB02A1B4 /* tempdProfile.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 8B15A12DCA07FC97206F610D /* tempdProfile.xcconfig */; };
		0F385A6580210796CCAC5B19 /* yhxtProfile.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 40D215AA607070B8C8B7839C /* yhxtProfile.xcconfig */; };
		0FE4AB6D16785031A4AD8C86 /* dyzbLaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = B8503E994F4968E30EF0B676 /* dyzbLaunchScreen.storyboard */; };
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		1816BEA48D2FED120DD0123E /* preProfile.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = C367195C606D31BAE9FA68A0 /* preProfile.xcconfig */; };
		1EE13E4FD72C2DB6103BC76D /* xyzqProfile.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 7A03DD76511C277116DC20F8 /* xyzqProfile.xcconfig */; };
		25F979FF78FC879A151BF9DF /* yhxtRelease.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 77F784468BAD1C3728050366 /* yhxtRelease.xcconfig */; };
		2C9392CFBD7CC877E354A35A /* rsypLaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = B64FD53CFE23A92955EE5E10 /* rsypLaunchScreen.storyboard */; };
		331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 331C807B294A618700263BE5 /* RunnerTests.swift */; };
		391E34B10281F551F46DF2F2 /* dyzbRelease.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = E01AC8251E0BAD7C72BBA68A /* dyzbRelease.xcconfig */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		3BC9FB49E1C3AF6B99B97D2B /* preRelease.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 317EAE2EAF6837154C0989ED /* preRelease.xcconfig */; };
		4203B3ED0E143A75AEFBAED3 /* preLaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 682ABCE32E76EB9C0A3828EA /* preLaunchScreen.storyboard */; };
		45AD3860688161983DD5A3F1 /* dyzbProfile.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 85FC5CAC153E59261EEC8786 /* dyzbProfile.xcconfig */; };
		4B226F18F9B3AE244A8E46FC /* rsypProfile.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = C915B1A37AA404BADEBA5BE0 /* rsypProfile.xcconfig */; };
		520483EE2065759F8B9FDA1E /* bszbProfile.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 31788A018A90998EB0E0108E /* bszbProfile.xcconfig */; };
		65C44E921400C98C979DBFBA /* xyzqDebug.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 621D73F63FFC06232B544CF6 /* xyzqDebug.xcconfig */; };
		72A28853545BCE51D3801808 /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 31F20D5ADD814FB08DEB9E31 /* Pods_Runner.framework */; };
		74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		7A7218283E5B3BA3212B4910 /* dyzbDebug.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = FBB00C17B517F6FD53F1703C /* dyzbDebug.xcconfig */; };
		7E6479DEEF64F85C1099E9C1 /* bszbLaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 21099D0B6904AC4258285242 /* bszbLaunchScreen.storyboard */; };
		809E1C8D1A78972024065C46 /* tempdLaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 82C177132C76FB48219D8FB8 /* tempdLaunchScreen.storyboard */; };
		816BE88007408A4AB19759A7 /* tempaDebug.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 649D3DD5BC6BDB225AE37B38 /* tempaDebug.xcconfig */; };
		968AD8D49CC723B0B664BA4A /* gpLaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 375984C48AF9FADFF47F7272 /* gpLaunchScreen.storyboard */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		A3F6B16E2F10BDC075C17738 /* gpDebug.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 728F315BD90CEC71C11FE13E /* gpDebug.xcconfig */; };
		AC8E5E4E0047E692DA489E33 /* xyzqLaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = E69B972E1E2ADBF1AFF6BB67 /* xyzqLaunchScreen.storyboard */; };
		B043D0BE8FB57B4867491279 /* gpRelease.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 9E069C6EBFE8681A0E01081C /* gpRelease.xcconfig */; };
		B0D1E33C1755D965B05684B4 /* tempaRelease.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 22B9FBD006788A9F25A8171B /* tempaRelease.xcconfig */; };
		B5BB9F6274CB0E70BDA8C7FA /* bszbRelease.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = DB8810A82054A9D7C3E31244 /* bszbRelease.xcconfig */; };
		BB750AA0644B32E6E96436A3 /* tempaProfile.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 512731BA6B3FB2DEDE4EBB46 /* tempaProfile.xcconfig */; };
		C0F034DBCBC62F17FD1F0E13 /* yhxtLaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 3FC5FA0911EC505D0F335FF9 /* yhxtLaunchScreen.storyboard */; };
		C297CACA0D8136A6648E339C /* tempdRelease.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = B065B9580AE74F4E2E4BC32A /* tempdRelease.xcconfig */; };
		C8AB44AB96EDF73D96291B74 /* rsypRelease.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 044AB9B9EB4408D1F1102DC5 /* rsypRelease.xcconfig */; };
		D0B31EB41371F0F21312659F /* tempdDebug.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = EEB9C24C1DC3C84855D87887 /* tempdDebug.xcconfig */; };
		D59433450CAAA0E0689E8279 /* preDebug.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 358C30C54CC0F61764B67471 /* preDebug.xcconfig */; };
		D7945FC7EF0878DF75737220 /* tempaLaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 972352EDB5D4525A9E2685E9 /* tempaLaunchScreen.storyboard */; };
		DC68930E6B7F6505083523E3 /* yhxtDebug.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = EA1E4914EEC2BDBAEC4C9B53 /* yhxtDebug.xcconfig */; };
		F03C3D4BF2C1A1DA5ADD6A59 /* bszbDebug.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 208E789D6C093B3397FC6DF8 /* bszbDebug.xcconfig */; };
		F0DFCB5303FCEA7FF5845046 /* gpProfile.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = B13ACDBFB0EE93B8F8D0DA38 /* gpProfile.xcconfig */; };
		FE4822D16C5CD8B9F98DE90E /* rsypDebug.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 69AA56ECA37AEAA89D964323 /* rsypDebug.xcconfig */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		331C8085294A63A400263BE5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97C146E61CF9000F007C117D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 97C146ED1CF9000F007C117D;
			remoteInfo = Runner;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		9705A1C41CF9048500538489 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		000DE0052E4B56DF0038CA8A /* Runner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		000DE0152E4C7D450038CA8A /* exportOptions_xyzq.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = exportOptions_xyzq.plist; sourceTree = "<group>"; };
		00228A792E38A4CF0036B054 /* exportOptions_dyzb.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = exportOptions_dyzb.plist; sourceTree = "<group>"; };
		00499B852DB67F2A0022EDD6 /* exportOptions_gp.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = exportOptions_gp.plist; sourceTree = "<group>"; };
		00499B862DB67F2A0022EDD6 /* exportOptions_rsyp.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = exportOptions_rsyp.plist; sourceTree = "<group>"; };
		004A6D972DCB9E57003AB024 /* exportOptions_pre.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = exportOptions_pre.plist; sourceTree = "<group>"; };
		00610DFB2DE445030050C2E4 /* exportOptions_yhxt.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = exportOptions_yhxt.plist; sourceTree = "<group>"; };
		0069894C2E27911800CB0F4A /* exportOptions_tempa.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = exportOptions_tempa.plist; sourceTree = "<group>"; };
		006989542E27954000CB0F4A /* exportOptions_bszb.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = exportOptions_bszb.plist; sourceTree = "<group>"; };
		044AB9B9EB4408D1F1102DC5 /* rsypRelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = rsypRelease.xcconfig; path = Flutter/rsypRelease.xcconfig; sourceTree = "<group>"; };
		05558DB3B5F6439B521DD463 /* Pods-RunnerTests.profile-tempa.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile-tempa.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile-tempa.xcconfig"; sourceTree = "<group>"; };
		0B0E932AF78098C07F9807B6 /* Pods-RunnerTests.profile-gp.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile-gp.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile-gp.xcconfig"; sourceTree = "<group>"; };
		0C4433F69AF9E6CAEB586DE0 /* Pods-RunnerTests.release-bszb.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release-bszb.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release-bszb.xcconfig"; sourceTree = "<group>"; };
		0D5D2865CEFEB6D2428B4579 /* Pods-Runner.release-rsyp.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-rsyp.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-rsyp.xcconfig"; sourceTree = "<group>"; };
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		15A427E12727F23D039D7736 /* Pods-RunnerTests.debug-pre.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug-pre.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug-pre.xcconfig"; sourceTree = "<group>"; };
		160ECB15E701B9023452BDBF /* Pods-RunnerTests.debug-tempa.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug-tempa.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug-tempa.xcconfig"; sourceTree = "<group>"; };
		1E3F352DAB2006538999C8C4 /* Pods-RunnerTests.release-dyzb.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release-dyzb.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release-dyzb.xcconfig"; sourceTree = "<group>"; };
		208E789D6C093B3397FC6DF8 /* bszbDebug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = bszbDebug.xcconfig; path = Flutter/bszbDebug.xcconfig; sourceTree = "<group>"; };
		21099D0B6904AC4258285242 /* bszbLaunchScreen.storyboard */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file.storyboard; name = bszbLaunchScreen.storyboard; path = Runner/bszbLaunchScreen.storyboard; sourceTree = "<group>"; };
		22B9FBD006788A9F25A8171B /* tempaRelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = tempaRelease.xcconfig; path = Flutter/tempaRelease.xcconfig; sourceTree = "<group>"; };
		2A1B2E8C8356B1507367E670 /* Pods-Runner.release-xyzq.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-xyzq.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-xyzq.xcconfig"; sourceTree = "<group>"; };
		31788A018A90998EB0E0108E /* bszbProfile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = bszbProfile.xcconfig; path = Flutter/bszbProfile.xcconfig; sourceTree = "<group>"; };
		317EAE2EAF6837154C0989ED /* preRelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = preRelease.xcconfig; path = Flutter/preRelease.xcconfig; sourceTree = "<group>"; };
		31F20D5ADD814FB08DEB9E31 /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		331C807B294A618700263BE5 /* RunnerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RunnerTests.swift; sourceTree = "<group>"; };
		331C8081294A63A400263BE5 /* RunnerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RunnerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		3410630F0A72851C1A4DFBD7 /* xyzqRelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = xyzqRelease.xcconfig; path = Flutter/xyzqRelease.xcconfig; sourceTree = "<group>"; };
		346F3F5457070844E03AC1DA /* Pods-Runner.debug-gp.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-gp.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-gp.xcconfig"; sourceTree = "<group>"; };
		358C30C54CC0F61764B67471 /* preDebug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = preDebug.xcconfig; path = Flutter/preDebug.xcconfig; sourceTree = "<group>"; };
		375984C48AF9FADFF47F7272 /* gpLaunchScreen.storyboard */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file.storyboard; name = gpLaunchScreen.storyboard; path = Runner/gpLaunchScreen.storyboard; sourceTree = "<group>"; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		3D3C1BAEE52805659101963B /* Pods-RunnerTests.profile-pre.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile-pre.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile-pre.xcconfig"; sourceTree = "<group>"; };
		3FC5FA0911EC505D0F335FF9 /* yhxtLaunchScreen.storyboard */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file.storyboard; name = yhxtLaunchScreen.storyboard; path = Runner/yhxtLaunchScreen.storyboard; sourceTree = "<group>"; };
		40D215AA607070B8C8B7839C /* yhxtProfile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = yhxtProfile.xcconfig; path = Flutter/yhxtProfile.xcconfig; sourceTree = "<group>"; };
		40DE84146F768888C07A5F17 /* Pods-RunnerTests.profile-rsyp.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile-rsyp.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile-rsyp.xcconfig"; sourceTree = "<group>"; };
		426A632FC6AA41776AE66047 /* Pods-Runner.release-gp.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-gp.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-gp.xcconfig"; sourceTree = "<group>"; };
		48920C621151078DB2336FAE /* Pods-RunnerTests.profile-dyzb.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile-dyzb.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile-dyzb.xcconfig"; sourceTree = "<group>"; };
		512731BA6B3FB2DEDE4EBB46 /* tempaProfile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = tempaProfile.xcconfig; path = Flutter/tempaProfile.xcconfig; sourceTree = "<group>"; };
		56EE557896CB230F651EC88F /* Pods-Runner.profile-xyzq.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile-xyzq.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile-xyzq.xcconfig"; sourceTree = "<group>"; };
		5C0B2EE5AF62CA02DE3AD652 /* Pods-RunnerTests.debug-dyzb.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug-dyzb.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug-dyzb.xcconfig"; sourceTree = "<group>"; };
		5C620A3F512B3EE233FF5425 /* Pods-RunnerTests.debug-rsyp.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug-rsyp.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug-rsyp.xcconfig"; sourceTree = "<group>"; };
		60C16F7255D8EF53050B27C8 /* Pods-Runner.release-tempa.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-tempa.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-tempa.xcconfig"; sourceTree = "<group>"; };
		6137CC9BE07EFD996CFEB6CC /* Pods-Runner.debug-pre.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-pre.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-pre.xcconfig"; sourceTree = "<group>"; };
		621D73F63FFC06232B544CF6 /* xyzqDebug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = xyzqDebug.xcconfig; path = Flutter/xyzqDebug.xcconfig; sourceTree = "<group>"; };
		622891AC07424D6CEC84FFB8 /* Pods-RunnerTests.debug-bszb.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug-bszb.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug-bszb.xcconfig"; sourceTree = "<group>"; };
		638C8EB2E3F663E876915943 /* Pods-RunnerTests.release-rsyp.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release-rsyp.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release-rsyp.xcconfig"; sourceTree = "<group>"; };
		649D3DD5BC6BDB225AE37B38 /* tempaDebug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = tempaDebug.xcconfig; path = Flutter/tempaDebug.xcconfig; sourceTree = "<group>"; };
		682ABCE32E76EB9C0A3828EA /* preLaunchScreen.storyboard */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file.storyboard; name = preLaunchScreen.storyboard; path = Runner/preLaunchScreen.storyboard; sourceTree = "<group>"; };
		69AA56ECA37AEAA89D964323 /* rsypDebug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = rsypDebug.xcconfig; path = Flutter/rsypDebug.xcconfig; sourceTree = "<group>"; };
		6E8D6F3A8FB2D8CB3027AF72 /* Pods-RunnerTests.release-gp.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release-gp.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release-gp.xcconfig"; sourceTree = "<group>"; };
		70FB09940D72593F0D0FCF3F /* Pods-Runner.profile-bszb.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile-bszb.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile-bszb.xcconfig"; sourceTree = "<group>"; };
		728F315BD90CEC71C11FE13E /* gpDebug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = gpDebug.xcconfig; path = Flutter/gpDebug.xcconfig; sourceTree = "<group>"; };
		73D6A0BA2176871891890D56 /* Pods-Runner.profile-tempa.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile-tempa.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile-tempa.xcconfig"; sourceTree = "<group>"; };
		74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		74858FAE1ED2DC5600515810 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		760E8D76F9C67196BF267FA4 /* Pods-Runner.release-dyzb.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-dyzb.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-dyzb.xcconfig"; sourceTree = "<group>"; };
		77F784468BAD1C3728050366 /* yhxtRelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = yhxtRelease.xcconfig; path = Flutter/yhxtRelease.xcconfig; sourceTree = "<group>"; };
		799C65DFE4DA24F501D44CD0 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		7A03DD76511C277116DC20F8 /* xyzqProfile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = xyzqProfile.xcconfig; path = Flutter/xyzqProfile.xcconfig; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		7EA91CBEB84CD4E1860AFE22 /* Pods-RunnerTests.profile-bszb.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile-bszb.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile-bszb.xcconfig"; sourceTree = "<group>"; };
		82C177132C76FB48219D8FB8 /* tempdLaunchScreen.storyboard */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file.storyboard; name = tempdLaunchScreen.storyboard; path = Runner/tempdLaunchScreen.storyboard; sourceTree = "<group>"; };
		85FC5CAC153E59261EEC8786 /* dyzbProfile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = dyzbProfile.xcconfig; path = Flutter/dyzbProfile.xcconfig; sourceTree = "<group>"; };
		888366C4F9C7726197968CA7 /* Pods-RunnerTests.profile-xyzq.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile-xyzq.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile-xyzq.xcconfig"; sourceTree = "<group>"; };
		8B15A12DCA07FC97206F610D /* tempdProfile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = tempdProfile.xcconfig; path = Flutter/tempdProfile.xcconfig; sourceTree = "<group>"; };
		8D4BF95393B3F60A73C743CB /* Pods-RunnerTests.release-pre.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release-pre.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release-pre.xcconfig"; sourceTree = "<group>"; };
		972352EDB5D4525A9E2685E9 /* tempaLaunchScreen.storyboard */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file.storyboard; name = tempaLaunchScreen.storyboard; path = Runner/tempaLaunchScreen.storyboard; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		9E069C6EBFE8681A0E01081C /* gpRelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = gpRelease.xcconfig; path = Flutter/gpRelease.xcconfig; sourceTree = "<group>"; };
		A1E5C1BE6521FDB1786ECC83 /* Pods-Runner.debug-tempa.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-tempa.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-tempa.xcconfig"; sourceTree = "<group>"; };
		A3279666A3A0E43F03E7ECBF /* Pods-RunnerTests.release-xyzq.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release-xyzq.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release-xyzq.xcconfig"; sourceTree = "<group>"; };
		A550F34B23D5D4B86949126A /* Pods-Runner.profile-gp.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile-gp.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile-gp.xcconfig"; sourceTree = "<group>"; };
		A723D236DB02B1ED7FE5EA05 /* Pods-Runner.profile-pre.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile-pre.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile-pre.xcconfig"; sourceTree = "<group>"; };
		A7B88E1B6C3C021CDCCDEFA9 /* Pods-RunnerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release.xcconfig"; sourceTree = "<group>"; };
		AEE111FB2B936CCCF33EA119 /* Pods_RunnerTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RunnerTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AF1ADC38A95EF42E3F634543 /* Pods-Runner.profile-rsyp.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile-rsyp.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile-rsyp.xcconfig"; sourceTree = "<group>"; };
		B065B9580AE74F4E2E4BC32A /* tempdRelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = tempdRelease.xcconfig; path = Flutter/tempdRelease.xcconfig; sourceTree = "<group>"; };
		B09EE81928F72A25E1B979B8 /* Pods-Runner.profile-yhxt.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile-yhxt.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile-yhxt.xcconfig"; sourceTree = "<group>"; };
		B13ACDBFB0EE93B8F8D0DA38 /* gpProfile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = gpProfile.xcconfig; path = Flutter/gpProfile.xcconfig; sourceTree = "<group>"; };
		B499CA44E92EC498C961055E /* Pods-Runner.debug-dyzb.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-dyzb.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-dyzb.xcconfig"; sourceTree = "<group>"; };
		B64FD53CFE23A92955EE5E10 /* rsypLaunchScreen.storyboard */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file.storyboard; name = rsypLaunchScreen.storyboard; path = Runner/rsypLaunchScreen.storyboard; sourceTree = "<group>"; };
		B6899B9EBAF0B63838A03621 /* Pods-RunnerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug.xcconfig"; sourceTree = "<group>"; };
		B8503E994F4968E30EF0B676 /* dyzbLaunchScreen.storyboard */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file.storyboard; name = dyzbLaunchScreen.storyboard; path = Runner/dyzbLaunchScreen.storyboard; sourceTree = "<group>"; };
		BE94BBF6E7A98A356F0E707E /* Pods-Runner.release-yhxt.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-yhxt.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-yhxt.xcconfig"; sourceTree = "<group>"; };
		C023B9456FE01AF8D747BAC6 /* Pods-Runner.release-bszb.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-bszb.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-bszb.xcconfig"; sourceTree = "<group>"; };
		C367195C606D31BAE9FA68A0 /* preProfile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = preProfile.xcconfig; path = Flutter/preProfile.xcconfig; sourceTree = "<group>"; };
		C58ACFA5A68F72996BB559E9 /* Pods-RunnerTests.release-yhxt.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release-yhxt.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release-yhxt.xcconfig"; sourceTree = "<group>"; };
		C7A478EBE9B74EF8F85C437C /* Pods-RunnerTests.profile-yhxt.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile-yhxt.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile-yhxt.xcconfig"; sourceTree = "<group>"; };
		C7CEF4419B5433AA75D8606D /* Pods-Runner.debug-yhxt.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-yhxt.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-yhxt.xcconfig"; sourceTree = "<group>"; };
		C8577FCAD1F84CEAFEE308FC /* Pods-RunnerTests.debug-gp.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug-gp.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug-gp.xcconfig"; sourceTree = "<group>"; };
		C915B1A37AA404BADEBA5BE0 /* rsypProfile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = rsypProfile.xcconfig; path = Flutter/rsypProfile.xcconfig; sourceTree = "<group>"; };
		CAF968F5B0587E54542435F7 /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
		CC7EE73BF0C202A9185CF8F5 /* Pods-RunnerTests.debug-xyzq.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug-xyzq.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug-xyzq.xcconfig"; sourceTree = "<group>"; };
		D2BBDA253A87FBC95EBAACCA /* Pods-Runner.debug-xyzq.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-xyzq.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-xyzq.xcconfig"; sourceTree = "<group>"; };
		D3C70A5B60C6CF26702A4FC8 /* Pods-Runner.profile-dyzb.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile-dyzb.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile-dyzb.xcconfig"; sourceTree = "<group>"; };
		DB8810A82054A9D7C3E31244 /* bszbRelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = bszbRelease.xcconfig; path = Flutter/bszbRelease.xcconfig; sourceTree = "<group>"; };
		DE29F2D2EB37392F8F090C5B /* Pods-Runner.debug-bszb.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-bszb.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-bszb.xcconfig"; sourceTree = "<group>"; };
		E01AC8251E0BAD7C72BBA68A /* dyzbRelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = dyzbRelease.xcconfig; path = Flutter/dyzbRelease.xcconfig; sourceTree = "<group>"; };
		E69B972E1E2ADBF1AFF6BB67 /* xyzqLaunchScreen.storyboard */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file.storyboard; name = xyzqLaunchScreen.storyboard; path = Runner/xyzqLaunchScreen.storyboard; sourceTree = "<group>"; };
		EA1E4914EEC2BDBAEC4C9B53 /* yhxtDebug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = yhxtDebug.xcconfig; path = Flutter/yhxtDebug.xcconfig; sourceTree = "<group>"; };
		EA6BABA37B29A8BC00D4D832 /* Pods-RunnerTests.debug-yhxt.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug-yhxt.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug-yhxt.xcconfig"; sourceTree = "<group>"; };
		ECC660FAFD3A06AAE373D777 /* Pods-RunnerTests.release-tempa.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release-tempa.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release-tempa.xcconfig"; sourceTree = "<group>"; };
		EEB9C24C1DC3C84855D87887 /* tempdDebug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = tempdDebug.xcconfig; path = Flutter/tempdDebug.xcconfig; sourceTree = "<group>"; };
		EFB3B9B9BA105D71F4EE9E34 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		F40A4EF796AB1CFA1C71780F /* Pods-Runner.debug-rsyp.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-rsyp.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-rsyp.xcconfig"; sourceTree = "<group>"; };
		F6EBDED64D276E29CE0E05AA /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		FA71B6181CD36BECEC89740B /* Pods-Runner.release-pre.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-pre.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-pre.xcconfig"; sourceTree = "<group>"; };
		FBB00C17B517F6FD53F1703C /* dyzbDebug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = dyzbDebug.xcconfig; path = Flutter/dyzbDebug.xcconfig; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				72A28853545BCE51D3801808 /* Pods_Runner.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE4CCC4E2A1D41E00FE749F2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				03733C236B0DFB3884EFBEE2 /* Pods_RunnerTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00499B872DB67F2A0022EDD6 /* CI */ = {
			isa = PBXGroup;
			children = (
				00228A792E38A4CF0036B054 /* exportOptions_dyzb.plist */,
				006989542E27954000CB0F4A /* exportOptions_bszb.plist */,
				0069894C2E27911800CB0F4A /* exportOptions_tempa.plist */,
				004A6D972DCB9E57003AB024 /* exportOptions_pre.plist */,
				00499B852DB67F2A0022EDD6 /* exportOptions_gp.plist */,
				00499B862DB67F2A0022EDD6 /* exportOptions_rsyp.plist */,
				00610DFB2DE445030050C2E4 /* exportOptions_yhxt.plist */,
				000DE0152E4C7D450038CA8A /* exportOptions_xyzq.plist */,
			);
			path = CI;
			sourceTree = "<group>";
		};
		331C8082294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXGroup;
			children = (
				331C807B294A618700263BE5 /* RunnerTests.swift */,
			);
			path = RunnerTests;
			sourceTree = "<group>";
		};
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
				728F315BD90CEC71C11FE13E /* gpDebug.xcconfig */,
				B13ACDBFB0EE93B8F8D0DA38 /* gpProfile.xcconfig */,
				9E069C6EBFE8681A0E01081C /* gpRelease.xcconfig */,
				69AA56ECA37AEAA89D964323 /* rsypDebug.xcconfig */,
				C915B1A37AA404BADEBA5BE0 /* rsypProfile.xcconfig */,
				044AB9B9EB4408D1F1102DC5 /* rsypRelease.xcconfig */,
				358C30C54CC0F61764B67471 /* preDebug.xcconfig */,
				C367195C606D31BAE9FA68A0 /* preProfile.xcconfig */,
				317EAE2EAF6837154C0989ED /* preRelease.xcconfig */,
				EA1E4914EEC2BDBAEC4C9B53 /* yhxtDebug.xcconfig */,
				40D215AA607070B8C8B7839C /* yhxtProfile.xcconfig */,
				77F784468BAD1C3728050366 /* yhxtRelease.xcconfig */,
				649D3DD5BC6BDB225AE37B38 /* tempaDebug.xcconfig */,
				512731BA6B3FB2DEDE4EBB46 /* tempaProfile.xcconfig */,
				22B9FBD006788A9F25A8171B /* tempaRelease.xcconfig */,
				EEB9C24C1DC3C84855D87887 /* tempdDebug.xcconfig */,
				8B15A12DCA07FC97206F610D /* tempdProfile.xcconfig */,
				B065B9580AE74F4E2E4BC32A /* tempdRelease.xcconfig */,
				208E789D6C093B3397FC6DF8 /* bszbDebug.xcconfig */,
				31788A018A90998EB0E0108E /* bszbProfile.xcconfig */,
				DB8810A82054A9D7C3E31244 /* bszbRelease.xcconfig */,
				FBB00C17B517F6FD53F1703C /* dyzbDebug.xcconfig */,
				85FC5CAC153E59261EEC8786 /* dyzbProfile.xcconfig */,
				E01AC8251E0BAD7C72BBA68A /* dyzbRelease.xcconfig */,
				621D73F63FFC06232B544CF6 /* xyzqDebug.xcconfig */,
				7A03DD76511C277116DC20F8 /* xyzqProfile.xcconfig */,
				3410630F0A72851C1A4DFBD7 /* xyzqRelease.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				00499B872DB67F2A0022EDD6 /* CI */,
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				97C146EF1CF9000F007C117D /* Products */,
				331C8082294A63A400263BE5 /* RunnerTests */,
				BF873764F5BC83A867653858 /* Pods */,
				375984C48AF9FADFF47F7272 /* gpLaunchScreen.storyboard */,
				B64FD53CFE23A92955EE5E10 /* rsypLaunchScreen.storyboard */,
				682ABCE32E76EB9C0A3828EA /* preLaunchScreen.storyboard */,
				3FC5FA0911EC505D0F335FF9 /* yhxtLaunchScreen.storyboard */,
				972352EDB5D4525A9E2685E9 /* tempaLaunchScreen.storyboard */,
				82C177132C76FB48219D8FB8 /* tempdLaunchScreen.storyboard */,
				21099D0B6904AC4258285242 /* bszbLaunchScreen.storyboard */,
				B8503E994F4968E30EF0B676 /* dyzbLaunchScreen.storyboard */,
				BBB1ADF0B2CC538D20CB4C2C /* Frameworks */,
				E69B972E1E2ADBF1AFF6BB67 /* xyzqLaunchScreen.storyboard */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
				331C8081294A63A400263BE5 /* RunnerTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				000DE0052E4B56DF0038CA8A /* Runner.entitlements */,
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				97C147021CF9000F007C117D /* Info.plist */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				74858FAE1ED2DC5600515810 /* AppDelegate.swift */,
				74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		BBB1ADF0B2CC538D20CB4C2C /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				31F20D5ADD814FB08DEB9E31 /* Pods_Runner.framework */,
				AEE111FB2B936CCCF33EA119 /* Pods_RunnerTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		BF873764F5BC83A867653858 /* Pods */ = {
			isa = PBXGroup;
			children = (
				EFB3B9B9BA105D71F4EE9E34 /* Pods-Runner.debug.xcconfig */,
				799C65DFE4DA24F501D44CD0 /* Pods-Runner.release.xcconfig */,
				F6EBDED64D276E29CE0E05AA /* Pods-Runner.profile.xcconfig */,
				346F3F5457070844E03AC1DA /* Pods-Runner.debug-gp.xcconfig */,
				A550F34B23D5D4B86949126A /* Pods-Runner.profile-gp.xcconfig */,
				426A632FC6AA41776AE66047 /* Pods-Runner.release-gp.xcconfig */,
				F40A4EF796AB1CFA1C71780F /* Pods-Runner.debug-rsyp.xcconfig */,
				AF1ADC38A95EF42E3F634543 /* Pods-Runner.profile-rsyp.xcconfig */,
				0D5D2865CEFEB6D2428B4579 /* Pods-Runner.release-rsyp.xcconfig */,
				6137CC9BE07EFD996CFEB6CC /* Pods-Runner.debug-pre.xcconfig */,
				A723D236DB02B1ED7FE5EA05 /* Pods-Runner.profile-pre.xcconfig */,
				FA71B6181CD36BECEC89740B /* Pods-Runner.release-pre.xcconfig */,
				C7CEF4419B5433AA75D8606D /* Pods-Runner.debug-yhxt.xcconfig */,
				B09EE81928F72A25E1B979B8 /* Pods-Runner.profile-yhxt.xcconfig */,
				BE94BBF6E7A98A356F0E707E /* Pods-Runner.release-yhxt.xcconfig */,
				A1E5C1BE6521FDB1786ECC83 /* Pods-Runner.debug-tempa.xcconfig */,
				73D6A0BA2176871891890D56 /* Pods-Runner.profile-tempa.xcconfig */,
				60C16F7255D8EF53050B27C8 /* Pods-Runner.release-tempa.xcconfig */,
				DE29F2D2EB37392F8F090C5B /* Pods-Runner.debug-bszb.xcconfig */,
				70FB09940D72593F0D0FCF3F /* Pods-Runner.profile-bszb.xcconfig */,
				C023B9456FE01AF8D747BAC6 /* Pods-Runner.release-bszb.xcconfig */,
				B499CA44E92EC498C961055E /* Pods-Runner.debug-dyzb.xcconfig */,
				D3C70A5B60C6CF26702A4FC8 /* Pods-Runner.profile-dyzb.xcconfig */,
				760E8D76F9C67196BF267FA4 /* Pods-Runner.release-dyzb.xcconfig */,
				B6899B9EBAF0B63838A03621 /* Pods-RunnerTests.debug.xcconfig */,
				A7B88E1B6C3C021CDCCDEFA9 /* Pods-RunnerTests.release.xcconfig */,
				CAF968F5B0587E54542435F7 /* Pods-RunnerTests.profile.xcconfig */,
				C8577FCAD1F84CEAFEE308FC /* Pods-RunnerTests.debug-gp.xcconfig */,
				0B0E932AF78098C07F9807B6 /* Pods-RunnerTests.profile-gp.xcconfig */,
				6E8D6F3A8FB2D8CB3027AF72 /* Pods-RunnerTests.release-gp.xcconfig */,
				5C620A3F512B3EE233FF5425 /* Pods-RunnerTests.debug-rsyp.xcconfig */,
				40DE84146F768888C07A5F17 /* Pods-RunnerTests.profile-rsyp.xcconfig */,
				638C8EB2E3F663E876915943 /* Pods-RunnerTests.release-rsyp.xcconfig */,
				15A427E12727F23D039D7736 /* Pods-RunnerTests.debug-pre.xcconfig */,
				3D3C1BAEE52805659101963B /* Pods-RunnerTests.profile-pre.xcconfig */,
				8D4BF95393B3F60A73C743CB /* Pods-RunnerTests.release-pre.xcconfig */,
				EA6BABA37B29A8BC00D4D832 /* Pods-RunnerTests.debug-yhxt.xcconfig */,
				C7A478EBE9B74EF8F85C437C /* Pods-RunnerTests.profile-yhxt.xcconfig */,
				C58ACFA5A68F72996BB559E9 /* Pods-RunnerTests.release-yhxt.xcconfig */,
				160ECB15E701B9023452BDBF /* Pods-RunnerTests.debug-tempa.xcconfig */,
				05558DB3B5F6439B521DD463 /* Pods-RunnerTests.profile-tempa.xcconfig */,
				ECC660FAFD3A06AAE373D777 /* Pods-RunnerTests.release-tempa.xcconfig */,
				622891AC07424D6CEC84FFB8 /* Pods-RunnerTests.debug-bszb.xcconfig */,
				7EA91CBEB84CD4E1860AFE22 /* Pods-RunnerTests.profile-bszb.xcconfig */,
				0C4433F69AF9E6CAEB586DE0 /* Pods-RunnerTests.release-bszb.xcconfig */,
				5C0B2EE5AF62CA02DE3AD652 /* Pods-RunnerTests.debug-dyzb.xcconfig */,
				48920C621151078DB2336FAE /* Pods-RunnerTests.profile-dyzb.xcconfig */,
				1E3F352DAB2006538999C8C4 /* Pods-RunnerTests.release-dyzb.xcconfig */,
				D2BBDA253A87FBC95EBAACCA /* Pods-Runner.debug-xyzq.xcconfig */,
				56EE557896CB230F651EC88F /* Pods-Runner.profile-xyzq.xcconfig */,
				2A1B2E8C8356B1507367E670 /* Pods-Runner.release-xyzq.xcconfig */,
				CC7EE73BF0C202A9185CF8F5 /* Pods-RunnerTests.debug-xyzq.xcconfig */,
				888366C4F9C7726197968CA7 /* Pods-RunnerTests.profile-xyzq.xcconfig */,
				A3279666A3A0E43F03E7ECBF /* Pods-RunnerTests.release-xyzq.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		331C8080294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */;
			buildPhases = (
				786AF2E5080F44C91B74068E /* [CP] Check Pods Manifest.lock */,
				331C807D294A63A400263BE5 /* Sources */,
				331C807F294A63A400263BE5 /* Resources */,
				EE4CCC4E2A1D41E00FE749F2 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				331C8086294A63A400263BE5 /* PBXTargetDependency */,
			);
			name = RunnerTests;
			productName = RunnerTests;
			productReference = 331C8081294A63A400263BE5 /* RunnerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				8B3391F24AC017419D59F7AD /* [CP] Check Pods Manifest.lock */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				9705A1C41CF9048500538489 /* Embed Frameworks */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				6817F45B0978A00D01C016EE /* [CP] Embed Pods Frameworks */,
				AFB8BFBC1696D15C586D4899 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					331C8080294A63A400263BE5 = {
						CreatedOnToolsVersion = 14.0;
						TestTargetID = 97C146ED1CF9000F007C117D;
					};
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
				331C8080294A63A400263BE5 /* RunnerTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		331C807F294A63A400263BE5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				004A6D982DCB9E57003AB024 /* exportOptions_pre.plist in Resources */,
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
				A3F6B16E2F10BDC075C17738 /* gpDebug.xcconfig in Resources */,
				F0DFCB5303FCEA7FF5845046 /* gpProfile.xcconfig in Resources */,
				B043D0BE8FB57B4867491279 /* gpRelease.xcconfig in Resources */,
				FE4822D16C5CD8B9F98DE90E /* rsypDebug.xcconfig in Resources */,
				00610DFC2DE445030050C2E4 /* exportOptions_yhxt.plist in Resources */,
				00499B882DB67F2A0022EDD6 /* exportOptions_gp.plist in Resources */,
				00499B892DB67F2A0022EDD6 /* exportOptions_rsyp.plist in Resources */,
				4B226F18F9B3AE244A8E46FC /* rsypProfile.xcconfig in Resources */,
				C8AB44AB96EDF73D96291B74 /* rsypRelease.xcconfig in Resources */,
				968AD8D49CC723B0B664BA4A /* gpLaunchScreen.storyboard in Resources */,
				2C9392CFBD7CC877E354A35A /* rsypLaunchScreen.storyboard in Resources */,
				D59433450CAAA0E0689E8279 /* preDebug.xcconfig in Resources */,
				1816BEA48D2FED120DD0123E /* preProfile.xcconfig in Resources */,
				3BC9FB49E1C3AF6B99B97D2B /* preRelease.xcconfig in Resources */,
				4203B3ED0E143A75AEFBAED3 /* preLaunchScreen.storyboard in Resources */,
				DC68930E6B7F6505083523E3 /* yhxtDebug.xcconfig in Resources */,
				0069894D2E27911800CB0F4A /* exportOptions_tempa.plist in Resources */,
				0F385A6580210796CCAC5B19 /* yhxtProfile.xcconfig in Resources */,
				25F979FF78FC879A151BF9DF /* yhxtRelease.xcconfig in Resources */,
				C0F034DBCBC62F17FD1F0E13 /* yhxtLaunchScreen.storyboard in Resources */,
				816BE88007408A4AB19759A7 /* tempaDebug.xcconfig in Resources */,
				BB750AA0644B32E6E96436A3 /* tempaProfile.xcconfig in Resources */,
				00C66E322E12A8F1007B75D9 /* BuildFile in Resources */,
				B0D1E33C1755D965B05684B4 /* tempaRelease.xcconfig in Resources */,
				006989552E27954000CB0F4A /* exportOptions_bszb.plist in Resources */,
				D7945FC7EF0878DF75737220 /* tempaLaunchScreen.storyboard in Resources */,
				D0B31EB41371F0F21312659F /* tempdDebug.xcconfig in Resources */,
				0435542A0B793D3BAB02A1B4 /* tempdProfile.xcconfig in Resources */,
				C297CACA0D8136A6648E339C /* tempdRelease.xcconfig in Resources */,
				809E1C8D1A78972024065C46 /* tempdLaunchScreen.storyboard in Resources */,
				F03C3D4BF2C1A1DA5ADD6A59 /* bszbDebug.xcconfig in Resources */,
				00228A7A2E38A4CF0036B054 /* exportOptions_dyzb.plist in Resources */,
				520483EE2065759F8B9FDA1E /* bszbProfile.xcconfig in Resources */,
				B5BB9F6274CB0E70BDA8C7FA /* bszbRelease.xcconfig in Resources */,
				000DE0162E4C7D450038CA8A /* exportOptions_xyzq.plist in Resources */,
				7E6479DEEF64F85C1099E9C1 /* bszbLaunchScreen.storyboard in Resources */,
				7A7218283E5B3BA3212B4910 /* dyzbDebug.xcconfig in Resources */,
				45AD3860688161983DD5A3F1 /* dyzbProfile.xcconfig in Resources */,
				391E34B10281F551F46DF2F2 /* dyzbRelease.xcconfig in Resources */,
				0FE4AB6D16785031A4AD8C86 /* dyzbLaunchScreen.storyboard in Resources */,
				65C44E921400C98C979DBFBA /* xyzqDebug.xcconfig in Resources */,
				1EE13E4FD72C2DB6103BC76D /* xyzqProfile.xcconfig in Resources */,
				0277366F835DAEC26FA6DBF3 /* xyzqRelease.xcconfig in Resources */,
				AC8E5E4E0047E692DA489E33 /* xyzqLaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin";
		};
		6817F45B0978A00D01C016EE /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		786AF2E5080F44C91B74068E /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RunnerTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		8B3391F24AC017419D59F7AD /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build";
		};
		AFB8BFBC1696D15C586D4899 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		331C807D294A63A400263BE5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		331C8086294A63A400263BE5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 97C146ED1CF9000F007C117D /* Runner */;
			targetProxy = 331C8085294A63A400263BE5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		000DE0122E4C77D00038CA8A /* Debug-xyzq */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CC7EE73BF0C202A9185CF8F5 /* Pods-RunnerTests.debug-xyzq.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Debug-xyzq";
		};
		000DE0132E4C77D00038CA8A /* Profile-xyzq */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 888366C4F9C7726197968CA7 /* Pods-RunnerTests.profile-xyzq.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Profile-xyzq";
		};
		000DE0142E4C77D00038CA8A /* Release-xyzq */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A3279666A3A0E43F03E7ECBF /* Pods-RunnerTests.release-xyzq.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Release-xyzq";
		};
		00228A762E38A4040036B054 /* Debug-dyzb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5C0B2EE5AF62CA02DE3AD652 /* Pods-RunnerTests.debug-dyzb.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Debug-dyzb";
		};
		00228A772E38A4040036B054 /* Profile-dyzb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 48920C621151078DB2336FAE /* Pods-RunnerTests.profile-dyzb.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Profile-dyzb";
		};
		00228A782E38A4040036B054 /* Release-dyzb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1E3F352DAB2006538999C8C4 /* Pods-RunnerTests.release-dyzb.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Release-dyzb";
		};
		004A6DAA2DCCBE85003AB024 /* Debug-pre */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 15A427E12727F23D039D7736 /* Pods-RunnerTests.debug-pre.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Debug-pre";
		};
		004A6DAB2DCCBE85003AB024 /* Profile-pre */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3D3C1BAEE52805659101963B /* Pods-RunnerTests.profile-pre.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Profile-pre";
		};
		004A6DAC2DCCBE85003AB024 /* Release-pre */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8D4BF95393B3F60A73C743CB /* Pods-RunnerTests.release-pre.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Release-pre";
		};
		00610DF82DE445010050C2E4 /* Debug-yhxt */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EA6BABA37B29A8BC00D4D832 /* Pods-RunnerTests.debug-yhxt.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Debug-yhxt";
		};
		00610DF92DE445010050C2E4 /* Profile-yhxt */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C7A478EBE9B74EF8F85C437C /* Pods-RunnerTests.profile-yhxt.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Profile-yhxt";
		};
		00610DFA2DE445010050C2E4 /* Release-yhxt */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C58ACFA5A68F72996BB559E9 /* Pods-RunnerTests.release-yhxt.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Release-yhxt";
		};
		006989512E2793CC00CB0F4A /* Debug-bszb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 622891AC07424D6CEC84FFB8 /* Pods-RunnerTests.debug-bszb.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Debug-bszb";
		};
		006989522E2793CC00CB0F4A /* Profile-bszb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7EA91CBEB84CD4E1860AFE22 /* Pods-RunnerTests.profile-bszb.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Profile-bszb";
		};
		006989532E2793CC00CB0F4A /* Release-bszb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0C4433F69AF9E6CAEB586DE0 /* Pods-RunnerTests.release-bszb.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Release-bszb";
		};
		00C66E2D2E12A8D5007B75D9 /* Debug-tempa */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 160ECB15E701B9023452BDBF /* Pods-RunnerTests.debug-tempa.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Debug-tempa";
		};
		00C66E2E2E12A8D5007B75D9 /* Profile-tempa */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 05558DB3B5F6439B521DD463 /* Pods-RunnerTests.profile-tempa.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Profile-tempa";
		};
		00C66E2F2E12A8D5007B75D9 /* Release-tempa */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ECC660FAFD3A06AAE373D777 /* Pods-RunnerTests.release-tempa.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Release-tempa";
		};
		0599C07198A9C1DE4A7E2107 /* Profile-xyzq */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7A03DD76511C277116DC20F8 /* xyzqProfile.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.xyzq.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Profile-xyzq";
		};
		09C35B394F7BE4EB18128156 /* Profile-dyzb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 85FC5CAC153E59261EEC8786 /* dyzbProfile.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Profile-dyzb";
		};
		0D5C8C12A0C57CA8C224AD53 /* Release-tempa */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 22B9FBD006788A9F25A8171B /* tempaRelease.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.tempa.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-tempa";
		};
		0F615B237153B22356B09624 /* Debug-bszb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 208E789D6C093B3397FC6DF8 /* bszbDebug.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.tempd.stock;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Debug-bszb";
		};
		152E205D72040BC94659D0AF /* Profile-rsyp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C915B1A37AA404BADEBA5BE0 /* rsypProfile.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.rsyp.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Profile-rsyp";
		};
		1D96675F83D20B2AF1945FDD /* Debug-gp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 728F315BD90CEC71C11FE13E /* gpDebug.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.gp.stock;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Debug-gp";
		};
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.gpmember.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		2819CEE98AEE01A115137D26 /* Debug-dyzb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FBB00C17B517F6FD53F1703C /* dyzbDebug.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.dyzb.stock;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Debug-dyzb";
		};
		300B9683E55011B08ACEE803 /* Release-dyzb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E01AC8251E0BAD7C72BBA68A /* dyzbRelease.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.dyzb.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-dyzb";
		};
		30E802EAC9B201F54A4E9DED /* Release-tempa */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 22B9FBD006788A9F25A8171B /* tempaRelease.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				PRODUCT_NAME = "$(TARGET_NAME)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = gpTempA_ad_hoc;
			};
			name = "Release-tempa";
		};
		331C8088294A63A400263BE5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B6899B9EBAF0B63838A03621 /* Pods-RunnerTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.gpmember.app.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Debug;
		};
		331C8089294A63A400263BE5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A7B88E1B6C3C021CDCCDEFA9 /* Pods-RunnerTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.gpmember.app.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Release;
		};
		331C808A294A63A400263BE5 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CAF968F5B0587E54542435F7 /* Pods-RunnerTests.profile.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.gpmember.app.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Profile;
		};
		34A3D4832E8B5AF01BB71DDE /* Profile-bszb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 31788A018A90998EB0E0108E /* bszbProfile.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.tempd.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Profile-bszb";
		};
		36077D29915D390C9F3798D1 /* Debug-pre */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 358C30C54CC0F61764B67471 /* preDebug.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Debug-pre";
		};
		361C3283A0BE9825A87818D1 /* Profile-xyzq */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7A03DD76511C277116DC20F8 /* xyzqProfile.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Profile-xyzq";
		};
		38359F4123EBF875FD7BD86A /* Release-bszb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DB8810A82054A9D7C3E31244 /* bszbRelease.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				PRODUCT_NAME = "$(TARGET_NAME)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = bszb_ad_hoc;
			};
			name = "Release-bszb";
		};
		3DD6622CB8CBC58DC749C6C1 /* Debug-tempa */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 649D3DD5BC6BDB225AE37B38 /* tempaDebug.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.tempa.stock;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Debug-tempa";
		};
		4A9F58FABD0F4D226B06AA78 /* Debug-yhxt */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EA1E4914EEC2BDBAEC4C9B53 /* yhxtDebug.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Debug-yhxt";
		};
		4AD7874D9343590F67C0A229 /* Release-pre */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 317EAE2EAF6837154C0989ED /* preRelease.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.gp.pre.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-pre";
		};
		52F7AD867DC8674A5682A538 /* Release-xyzq */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3410630F0A72851C1A4DFBD7 /* xyzqRelease.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = xyzq_ad_hoc;
			};
			name = "Release-xyzq";
		};
		54F50F19A7B53B4C8151EACA /* Profile-tempa */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 512731BA6B3FB2DEDE4EBB46 /* tempaProfile.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.tempa.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Profile-tempa";
		};
		5A6C78BBC4A270443D030E1A /* Profile-tempa */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 512731BA6B3FB2DEDE4EBB46 /* tempaProfile.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Profile-tempa";
		};
		5AC5BD4ACAF7885A323BD4AD /* Release-gp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9E069C6EBFE8681A0E01081C /* gpRelease.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.gp.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-gp";
		};
		7E2EDB9F4B804075FA1A313F /* Release-pre */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 317EAE2EAF6837154C0989ED /* preRelease.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				PRODUCT_NAME = "$(TARGET_NAME)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = gppre_ad_hoc;
			};
			name = "Release-pre";
		};
		8856BA19FED61C253D8A19B3 /* Profile-yhxt */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 40D215AA607070B8C8B7839C /* yhxtProfile.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Profile-yhxt";
		};
		88A0C2127042B2BF1194D40B /* Debug-gp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 728F315BD90CEC71C11FE13E /* gpDebug.xcconfig */;
			buildSettings = {
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "i386, arm64";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Debug-gp";
		};
		8CC9398A6D663DF59CB3647E /* Debug-bszb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 208E789D6C093B3397FC6DF8 /* bszbDebug.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Debug-bszb";
		};
		8E0395ABD9217BE81D98AEB7 /* Release-xyzq */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3410630F0A72851C1A4DFBD7 /* xyzqRelease.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.xyzq.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-xyzq";
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "i386, arm64";
				INFOPLIST_FILE = Runner/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.gpmember.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.gpmember.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		9A38C313D1FD86F8EC15353F /* Profile-dyzb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 85FC5CAC153E59261EEC8786 /* dyzbProfile.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.dyzb.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Profile-dyzb";
		};
		A67A07AA779050DB886C7FBC /* Release-yhxt */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 77F784468BAD1C3728050366 /* yhxtRelease.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				PRODUCT_NAME = "$(TARGET_NAME)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = yhxt_ad_hoc;
			};
			name = "Release-yhxt";
		};
		A94346A5AF4900BAF61D4762 /* Release-dyzb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E01AC8251E0BAD7C72BBA68A /* dyzbRelease.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = dyzb_ad_hoc;
			};
			name = "Release-dyzb";
		};
		B1A17D702DAF944600DE95AD /* Debug-gp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C8577FCAD1F84CEAFEE308FC /* Pods-RunnerTests.debug-gp.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Debug-gp";
		};
		B1A17D712DAF944600DE95AD /* Profile-gp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0B0E932AF78098C07F9807B6 /* Pods-RunnerTests.profile-gp.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Profile-gp";
		};
		B1A17D722DAF944600DE95AD /* Release-gp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6E8D6F3A8FB2D8CB3027AF72 /* Pods-RunnerTests.release-gp.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Release-gp";
		};
		B1A17D732DAF944600DE95AD /* Debug-rsyp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5C620A3F512B3EE233FF5425 /* Pods-RunnerTests.debug-rsyp.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Debug-rsyp";
		};
		B1A17D742DAF944600DE95AD /* Profile-rsyp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 40DE84146F768888C07A5F17 /* Pods-RunnerTests.profile-rsyp.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Profile-rsyp";
		};
		B1A17D752DAF944600DE95AD /* Release-rsyp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 638C8EB2E3F663E876915943 /* Pods-RunnerTests.release-rsyp.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = RunnerTests;
			};
			name = "Release-rsyp";
		};
		C115D4E6438B414599C8A339 /* Debug-pre */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 358C30C54CC0F61764B67471 /* preDebug.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.gp.pre.stock;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Debug-pre";
		};
		C1B453D85FBF5AC29546D263 /* Debug-dyzb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FBB00C17B517F6FD53F1703C /* dyzbDebug.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Debug-dyzb";
		};
		C3763D91D70A402773D7DA12 /* Profile-pre */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C367195C606D31BAE9FA68A0 /* preProfile.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Profile-pre";
		};
		C8D28A72C3F89B2311F37A8D /* Release-gp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9E069C6EBFE8681A0E01081C /* gpRelease.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				PRODUCT_NAME = "$(TARGET_NAME)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = gp_ad_hoc;
			};
			name = "Release-gp";
		};
		C8E69F5D66229E6A6DB155CF /* Debug-xyzq */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 621D73F63FFC06232B544CF6 /* xyzqDebug.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Debug-xyzq";
		};
		CA498B1671A45C7042F5074E /* Debug-rsyp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 69AA56ECA37AEAA89D964323 /* rsypDebug.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.rsyp.stock;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Debug-rsyp";
		};
		CB32F7962FDD90A835F6C55C /* Release-rsyp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 044AB9B9EB4408D1F1102DC5 /* rsypRelease.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				PRODUCT_NAME = "$(TARGET_NAME)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = rsyp_ad_hoc;
			};
			name = "Release-rsyp";
		};
		CDF8789757E2737C8EFDFF88 /* Profile-gp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B13ACDBFB0EE93B8F8D0DA38 /* gpProfile.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.gp.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Profile-gp";
		};
		D65EB38C766A3C058F6B2D82 /* Release-rsyp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 044AB9B9EB4408D1F1102DC5 /* rsypRelease.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.rsyp.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-rsyp";
		};
		D8228E2E3AF66316E8C5A73C /* Profile-pre */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C367195C606D31BAE9FA68A0 /* preProfile.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.gp.pre.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Profile-pre";
		};
		DAE9DD7F8387C5AE12E26DEC /* Debug-tempa */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 649D3DD5BC6BDB225AE37B38 /* tempaDebug.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Debug-tempa";
		};
		DDDAC402B1BF7A60BA3C174F /* Profile-yhxt */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 40D215AA607070B8C8B7839C /* yhxtProfile.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.yhxt.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Profile-yhxt";
		};
		E8F2411980B08D6F1D71FA64 /* Debug-yhxt */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EA1E4914EEC2BDBAEC4C9B53 /* yhxtDebug.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.yhxt.stock;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Debug-yhxt";
		};
		E91AAD95655790BF9FB3DBAB /* Debug-xyzq */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 621D73F63FFC06232B544CF6 /* xyzqDebug.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.xyzq.stock;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Debug-xyzq";
		};
		ED17881FED8BBCDA075E15E2 /* Profile-bszb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 31788A018A90998EB0E0108E /* bszbProfile.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Profile-bszb";
		};
		F0546D7641D9012EDB6336EB /* Profile-rsyp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C915B1A37AA404BADEBA5BE0 /* rsypProfile.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Profile-rsyp";
		};
		F8142F1B78D19BB226FAC1CF /* Debug-rsyp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 69AA56ECA37AEAA89D964323 /* rsypDebug.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Debug-rsyp";
		};
		F8725DB637B145D5DA59C2BC /* Profile-gp */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B13ACDBFB0EE93B8F8D0DA38 /* gpProfile.xcconfig */;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Profile-gp";
		};
		F96E80C4599C23A8FD6F1AE5 /* Release-yhxt */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 77F784468BAD1C3728050366 /* yhxtRelease.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.yhxt.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-yhxt";
		};
		FFC797B734E6037A17BF301C /* Release-bszb */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DB8810A82054A9D7C3E31244 /* bszbRelease.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = "$(ASSET_PREFIX)AppIcon";
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.tempd.stock;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-bszb";
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				331C8088294A63A400263BE5 /* Debug */,
				331C8089294A63A400263BE5 /* Release */,
				331C808A294A63A400263BE5 /* Profile */,
				B1A17D702DAF944600DE95AD /* Debug-gp */,
				B1A17D712DAF944600DE95AD /* Profile-gp */,
				B1A17D722DAF944600DE95AD /* Release-gp */,
				B1A17D732DAF944600DE95AD /* Debug-rsyp */,
				B1A17D742DAF944600DE95AD /* Profile-rsyp */,
				B1A17D752DAF944600DE95AD /* Release-rsyp */,
				004A6DAA2DCCBE85003AB024 /* Debug-pre */,
				004A6DAB2DCCBE85003AB024 /* Profile-pre */,
				004A6DAC2DCCBE85003AB024 /* Release-pre */,
				00610DF82DE445010050C2E4 /* Debug-yhxt */,
				00610DF92DE445010050C2E4 /* Profile-yhxt */,
				00610DFA2DE445010050C2E4 /* Release-yhxt */,
				00C66E2D2E12A8D5007B75D9 /* Debug-tempa */,
				00C66E2E2E12A8D5007B75D9 /* Profile-tempa */,
				00C66E2F2E12A8D5007B75D9 /* Release-tempa */,
				006989512E2793CC00CB0F4A /* Debug-bszb */,
				006989522E2793CC00CB0F4A /* Profile-bszb */,
				006989532E2793CC00CB0F4A /* Release-bszb */,
				00228A762E38A4040036B054 /* Debug-dyzb */,
				00228A772E38A4040036B054 /* Profile-dyzb */,
				00228A782E38A4040036B054 /* Release-dyzb */,
				000DE0122E4C77D00038CA8A /* Debug-xyzq */,
				000DE0132E4C77D00038CA8A /* Profile-xyzq */,
				000DE0142E4C77D00038CA8A /* Release-xyzq */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				249021D3217E4FDB00AE95B9 /* Profile */,
				1D96675F83D20B2AF1945FDD /* Debug-gp */,
				CDF8789757E2737C8EFDFF88 /* Profile-gp */,
				5AC5BD4ACAF7885A323BD4AD /* Release-gp */,
				CA498B1671A45C7042F5074E /* Debug-rsyp */,
				152E205D72040BC94659D0AF /* Profile-rsyp */,
				D65EB38C766A3C058F6B2D82 /* Release-rsyp */,
				C115D4E6438B414599C8A339 /* Debug-pre */,
				D8228E2E3AF66316E8C5A73C /* Profile-pre */,
				4AD7874D9343590F67C0A229 /* Release-pre */,
				E8F2411980B08D6F1D71FA64 /* Debug-yhxt */,
				DDDAC402B1BF7A60BA3C174F /* Profile-yhxt */,
				F96E80C4599C23A8FD6F1AE5 /* Release-yhxt */,
				3DD6622CB8CBC58DC749C6C1 /* Debug-tempa */,
				54F50F19A7B53B4C8151EACA /* Profile-tempa */,
				0D5C8C12A0C57CA8C224AD53 /* Release-tempa */,
				0F615B237153B22356B09624 /* Debug-bszb */,
				34A3D4832E8B5AF01BB71DDE /* Profile-bszb */,
				FFC797B734E6037A17BF301C /* Release-bszb */,
				2819CEE98AEE01A115137D26 /* Debug-dyzb */,
				9A38C313D1FD86F8EC15353F /* Profile-dyzb */,
				300B9683E55011B08ACEE803 /* Release-dyzb */,
				E91AAD95655790BF9FB3DBAB /* Debug-xyzq */,
				0599C07198A9C1DE4A7E2107 /* Profile-xyzq */,
				8E0395ABD9217BE81D98AEB7 /* Release-xyzq */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				249021D4217E4FDB00AE95B9 /* Profile */,
				88A0C2127042B2BF1194D40B /* Debug-gp */,
				F8725DB637B145D5DA59C2BC /* Profile-gp */,
				C8D28A72C3F89B2311F37A8D /* Release-gp */,
				F8142F1B78D19BB226FAC1CF /* Debug-rsyp */,
				F0546D7641D9012EDB6336EB /* Profile-rsyp */,
				CB32F7962FDD90A835F6C55C /* Release-rsyp */,
				36077D29915D390C9F3798D1 /* Debug-pre */,
				C3763D91D70A402773D7DA12 /* Profile-pre */,
				7E2EDB9F4B804075FA1A313F /* Release-pre */,
				4A9F58FABD0F4D226B06AA78 /* Debug-yhxt */,
				8856BA19FED61C253D8A19B3 /* Profile-yhxt */,
				A67A07AA779050DB886C7FBC /* Release-yhxt */,
				DAE9DD7F8387C5AE12E26DEC /* Debug-tempa */,
				5A6C78BBC4A270443D030E1A /* Profile-tempa */,
				30E802EAC9B201F54A4E9DED /* Release-tempa */,
				8CC9398A6D663DF59CB3647E /* Debug-bszb */,
				ED17881FED8BBCDA075E15E2 /* Profile-bszb */,
				38359F4123EBF875FD7BD86A /* Release-bszb */,
				C1B453D85FBF5AC29546D263 /* Debug-dyzb */,
				09C35B394F7BE4EB18128156 /* Profile-dyzb */,
				A94346A5AF4900BAF61D4762 /* Release-dyzb */,
				C8E69F5D66229E6A6DB155CF /* Debug-xyzq */,
				361C3283A0BE9825A87818D1 /* Profile-xyzq */,
				52F7AD867DC8674A5682A538 /* Release-xyzq */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
